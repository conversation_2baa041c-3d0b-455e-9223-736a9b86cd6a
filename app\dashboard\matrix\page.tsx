'use client';

import { useState, useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import AppLayout from '@/components/AppLayout';
import ProjectSelectionModal from '@/components/ProjectSelectionModal';
import ProjectDropdown from '@/components/ProjectDropdown';
import KeywordForm from '@/components/KeywordForm';
import { useAuth } from '@/context/AuthContext';
import { FormData, Matrix, Project } from '@/types';
import { generateMockWebhookData } from '@/utils/mockData';
import { generateMatrixId } from '@/utils/matrixUtils';

// Dashboard Matrix content component that uses searchParams
function DashboardMatrixContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const projectId = searchParams ? searchParams.get('projectId') : null;

  // Log the projectId from URL for debugging
  console.log('Matrix page loaded with projectId from URL:', projectId);

  const { user, isLoading: authLoading } = useAuth();
  const [isProjectSelectionModalOpen, setIsProjectSelectionModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [generatedMatrix, setGeneratedMatrix] = useState<Matrix | null>(null);
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!authLoading && !user) {
      router.push('/login-with-otp');
    }
  }, [user, authLoading, router]);

  // Handle project selection
  const handleProjectSelect = (project: Project) => {
    setSelectedProject(project);
  };

  // Handle form submission
  const handleFormSubmit = async (formData: FormData) => {
    if (!user || !selectedProject) return;

    setIsLoading(true);
    setError(null);

    try {
      // First, send the data to the webhook for processing
      console.log('🚀 Sending matrix generation request to webhook...');

      const webhookData = {
        userId: user.id,
        plan: user.plan || 'free',
        mainKeyword: formData.mainKeyword,
        location: formData.location,
        language: formData.language,
      };

      // Import the webhook function dynamically to avoid SSR issues
      const { sendMatrixGenerationToWebhook } = await import('@/utils/webhookUtils');

      try {
        const webhookResponse = await sendMatrixGenerationToWebhook(webhookData);
        console.log('✅ Webhook response received:', webhookResponse);

        // The webhook will process the data in the background
        // For now, we'll continue with the existing flow to create a matrix entry

      } catch (webhookError) {
        console.warn('⚠️ Webhook call failed, continuing with local processing:', webhookError);
        // Continue with the existing flow even if webhook fails
      }

      // Generate mock data for immediate display (while webhook processes in background)
      const mockData = generateMockWebhookData(
        formData.mainKeyword,
        formData.location,
        formData.language
      );

      let matrix: any;

      try {
        // Try to create matrix via API
        const response = await fetch('/api/matrices', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify({
            projectId: selectedProject.id,
            mainKeyword: formData.mainKeyword,
            location: formData.location,
            language: formData.language,
            keywordResearch: mockData.keywordResearch,
            contentMatrix: mockData.contentMatrix,
          }),
        });

        if (!response.ok) {
          throw new Error('API call failed');
        }

        const data = await response.json();
        matrix = data.matrix;

        console.log('✅ Matrix created via API:', matrix);
      } catch (apiError) {
        console.warn('⚠️ API failed, using fallback approach:', apiError);

        // Fallback: Create matrix with mock ID for demo purposes
        const keywordSlug = formData.mainKeyword.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '');
        const matrixId = `${keywordSlug}-${Date.now()}`;

        matrix = {
          _id: matrixId,
          projectId: selectedProject.id,
          userId: user.id,
          mainKeyword: formData.mainKeyword,
          filename: `${keywordSlug}-matrix.json`,
          location: formData.location,
          language: formData.language,
          keywordResearch: mockData.keywordResearch,
          contentMatrix: mockData.contentMatrix,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };

        console.log('🔄 Using fallback matrix:', matrix);
      }

      // Format matrix for state
      const newMatrix: Matrix = {
        id: matrix._id,
        projectId: matrix.projectId,
        userId: matrix.userId,
        mainKeyword: matrix.mainKeyword,
        filename: matrix.filename,
        location: matrix.location,
        language: matrix.language,
        keywordResearch: matrix.keywordResearch,
        contentMatrix: matrix.contentMatrix,
        createdAt: matrix.createdAt,
        updatedAt: matrix.updatedAt,
      };

      setGeneratedMatrix(newMatrix);

      console.log('📋 Formatted matrix:', newMatrix);

      // Ensure matrix has a valid ID before redirecting
      const matrixId = matrix._id || generateMatrixId(formData.mainKeyword);

      // Create project slug for routing
      const projectSlug = selectedProject.name.toLowerCase().replace(/\s+/g, '-');
      const redirectUrl = `/project/${projectSlug}/matrix/${matrixId}`;

      console.log('🔄 Redirecting to matrix results:', redirectUrl);

      // Redirect to the matrix results page
      setTimeout(() => {
        router.push(redirectUrl);
      }, 1000); // Give user time to see the success message

    } catch (err) {
      console.error('Error generating matrix:', err);
      setError('Failed to generate matrix. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <ProjectSelectionModal
        isOpen={isProjectSelectionModalOpen}
        onClose={() => setIsProjectSelectionModalOpen(false)}
      />

      <div className="space-y-8">
        <div className="bg-white shadow-md rounded-lg p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Matrix Generator</h1>
          <p className="text-gray-600">
            Create and manage your SEO content matrices. Select a project and enter a keyword to get started.
          </p>
        </div>

        {/* Project Dropdown */}
        <ProjectDropdown
          onProjectSelect={handleProjectSelect}
          initialProjectId={projectId || undefined}
        />

        {/* Show Keyword Form only after project selection */}
        {selectedProject && (
          isLoading ? (
            <div className="bg-white shadow-md rounded-lg p-6">
              <div className="animate-pulse">
                <div className="h-8 bg-gray-200 rounded mb-4 w-1/3"></div>
                <div className="h-4 bg-gray-200 rounded mb-6 w-1/2"></div>
                <div className="h-64 bg-gray-200 rounded"></div>
              </div>
            </div>
          ) : (
            <KeywordForm onSubmit={handleFormSubmit} />
          )
        )}

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative">
            {error}
          </div>
        )}

        {generatedMatrix && (
          <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded relative">
            Matrix generated successfully for keyword: <strong>{generatedMatrix.mainKeyword}</strong> in project: <strong>{selectedProject?.name}</strong>
          </div>
        )}
      </div>
    </>
  );
}

// Main page component with Suspense
export default function DashboardMatrixPage() {
  return (
    <AppLayout>
      <Suspense fallback={
        <div className="space-y-8">
          <div className="bg-white shadow-md rounded-lg p-6">
            <div className="animate-pulse">
              <div className="h-8 bg-gray-200 rounded mb-4 w-1/3"></div>
              <div className="h-4 bg-gray-200 rounded mb-6 w-1/2"></div>
              <div className="h-64 bg-gray-200 rounded"></div>
            </div>
          </div>
        </div>
      }>
        <DashboardMatrixContent />
      </Suspense>
    </AppLayout>
  );
}
