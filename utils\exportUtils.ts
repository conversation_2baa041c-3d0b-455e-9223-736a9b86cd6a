import * as XLSX from 'xlsx';
import <PERSON> from 'papapar<PERSON>';
import { KeywordResearchItem, ContentMatrixItem } from '@/types';

export interface ExportData {
  keywordResearch: KeywordResearchItem[];
  contentMatrix: ContentMatrixItem[];
  projectName: string;
  mainKeyword: string;
  location: string;
  language: string;
  exportDate: string;
}

// Format keyword research data for export
const formatKeywordResearchForExport = (data: KeywordResearchItem[]) => {
  return data.map((item, index) => ({
    'S.No.': index + 1,
    'Main Keyword': item.mainKeyword,
    'Type': item.type,
    'Keyword': item.keyword,
    'MSV': item.msv,
    'Search Intent': item.searchIntent,
    'KW Difficulty': item.kwDifficulty,
    'Competition': item.competition,
    'CPC': item.cpc,
    'Answer': item.answer,
    'Timestamp': item.timestamp,
  }));
};

// Format content matrix data for export
const formatContentMatrixForExport = (data: ContentMatrixItem[]) => {
  return data.map((item, index) => ({
    'S.No.': index + 1,
    'Main Keyword': item.mainKeyword,
    'Cluster': item.cluster,
    'Content Type': item.contentType,
    'Focus': item.focus,
    'Category': item.category,
    'URL': item.url,
    'Search Volume': item.searchVol,
    'Keywords': item.keywords,
    'Status': item.status,
  }));
};

// Export to CSV format
export const exportToCSV = (exportData: ExportData) => {
  const { keywordResearch, contentMatrix, projectName, mainKeyword, exportDate } = exportData;
  
  // Format data
  const formattedKeywordData = formatKeywordResearchForExport(keywordResearch);
  const formattedContentData = formatContentMatrixForExport(contentMatrix);
  
  // Create CSV content for keyword research
  const keywordCSV = Papa.unparse(formattedKeywordData);
  
  // Create CSV content for content matrix
  const contentCSV = Papa.unparse(formattedContentData);
  
  // Create combined CSV with headers
  const combinedCSV = [
    `Project: ${projectName}`,
    `Main Keyword: ${mainKeyword}`,
    `Export Date: ${exportDate}`,
    '',
    'KEYWORD RESEARCH DATA',
    '',
    keywordCSV,
    '',
    '',
    'CONTENT MATRIX DATA',
    '',
    contentCSV
  ].join('\n');
  
  // Download the file
  const blob = new Blob([combinedCSV], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);
  link.setAttribute('href', url);
  link.setAttribute('download', `${mainKeyword.toLowerCase().replace(/\s+/g, '-')}-matrix-export.csv`);
  link.style.visibility = 'hidden';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

// Export to Excel format
export const exportToExcel = (exportData: ExportData) => {
  const { keywordResearch, contentMatrix, projectName, mainKeyword, location, language, exportDate } = exportData;
  
  // Format data
  const formattedKeywordData = formatKeywordResearchForExport(keywordResearch);
  const formattedContentData = formatContentMatrixForExport(contentMatrix);
  
  // Create workbook
  const workbook = XLSX.utils.book_new();
  
  // Create summary sheet
  const summaryData = [
    ['Project Information'],
    ['Project Name', projectName],
    ['Main Keyword', mainKeyword],
    ['Location', location],
    ['Language', language],
    ['Export Date', exportDate],
    [''],
    ['Summary Statistics'],
    ['Total Keywords', keywordResearch.length],
    ['Total Content Pieces', contentMatrix.length],
    ['Total Search Volume', keywordResearch.reduce((sum, item) => sum + item.msv, 0)],
    ['Average Search Volume', keywordResearch.length > 0 ? Math.round(keywordResearch.reduce((sum, item) => sum + item.msv, 0) / keywordResearch.length) : 0],
  ];
  
  const summarySheet = XLSX.utils.aoa_to_sheet(summaryData);
  XLSX.utils.book_append_sheet(workbook, summarySheet, 'Summary');
  
  // Create keyword research sheet
  const keywordSheet = XLSX.utils.json_to_sheet(formattedKeywordData);
  XLSX.utils.book_append_sheet(workbook, keywordSheet, 'Keyword Research');
  
  // Create content matrix sheet
  const contentSheet = XLSX.utils.json_to_sheet(formattedContentData);
  XLSX.utils.book_append_sheet(workbook, contentSheet, 'Content Matrix');
  
  // Save the file
  XLSX.writeFile(workbook, `${mainKeyword.toLowerCase().replace(/\s+/g, '-')}-matrix-export.xlsx`);
};

// Export to JSON format (existing functionality)
export const exportToJSON = (exportData: ExportData) => {
  const dataStr = JSON.stringify(exportData, null, 2);
  const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
  const exportFileDefaultName = `${exportData.mainKeyword.toLowerCase().replace(/\s+/g, '-')}-matrix-export.json`;
  const linkElement = document.createElement('a');
  linkElement.setAttribute('href', dataUri);
  linkElement.setAttribute('download', exportFileDefaultName);
  linkElement.click();
};
