// Test script to verify CORS fix for matrix webhook
// This simulates a client-side call to the matrix webhook proxy

const testMatrixWebhookProxy = async () => {
  const testData = {
    userId: 'test-user-123',
    plan: 'standard',
    mainKeyword: 'ai tools for productivity',
    location: 'United States',
    language: 'English',
    timestamp: Date.now(),
    debug: {
      source: 'matrix-app',
      clientTime: new Date().toISOString(),
      environment: 'test',
      runtime: 'browser',
      action: 'matrix-generation'
    }
  };

  const MATRIX_WEBHOOK_PROXY_URL = 'http://localhost:3001/api/matrix-webhook-proxy';

  try {
    console.log('🚀 Testing matrix webhook proxy...');
    console.log('📤 Sending data:', JSON.stringify(testData, null, 2));

    const response = await fetch(MATRIX_WEBHOOK_PROXY_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Debug-Info': 'Test matrix webhook proxy call',
      },
      body: JSON.stringify(testData),
    });

    console.log('📊 Response status:', response.status);
    console.log('📊 Response headers:', Object.fromEntries(response.headers.entries()));

    if (response.ok) {
      const responseData = await response.json();
      console.log('✅ Success! Response data:', JSON.stringify(responseData, null, 2));
      return responseData;
    } else {
      const errorData = await response.text();
      console.error('❌ Error response:', errorData);
      throw new Error(`HTTP ${response.status}: ${errorData}`);
    }
  } catch (error) {
    console.error('❌ Test failed:', error);
    throw error;
  }
};

// Run the test
testMatrixWebhookProxy()
  .then(() => {
    console.log('🎉 CORS fix test completed successfully!');
  })
  .catch((error) => {
    console.error('💥 CORS fix test failed:', error);
  });
