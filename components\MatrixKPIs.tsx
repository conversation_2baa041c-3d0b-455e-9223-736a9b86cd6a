import React from 'react';
import { KeywordResearchItem, ContentMatrixItem } from '@/types';

interface MatrixKPIsProps {
  keywordData: KeywordResearchItem[];
  contentData: ContentMatrixItem[];
  mainKeyword: string;
  location: string;
  language: string;
}

const MatrixKPIs: React.FC<MatrixKPIsProps> = ({
  keywordData,
  contentData,
  mainKeyword,
  location,
  language,
}) => {
  // Calculate KPI metrics
  const totalKeywords = keywordData.length;
  const totalContentPieces = contentData.length;
  
  // Calculate average keyword difficulty
  const avgKeywordDifficulty = keywordData.length > 0 
    ? Math.round(keywordData.reduce((sum, item) => {
        // Convert difficulty to numeric value for calculation
        const difficultyValue = typeof item.kwDifficulty === 'string' 
          ? (item.kwDifficulty === 'LOW' ? 1 : item.kwDifficulty === 'MEDIUM' ? 2 : 3)
          : item.kwDifficulty;
        return sum + difficultyValue;
      }, 0) / keywordData.length)
    : 0;
  
  // Convert back to string representation
  const avgDifficultyLabel = avgKeywordDifficulty <= 1.5 ? 'LOW' : 
                            avgKeywordDifficulty <= 2.5 ? 'MEDIUM' : 'HIGH';
  
  // High opportunity keywords (low competition, decent volume)
  const highOpportunityKeywords = keywordData.filter(
    item => {
      const difficulty = typeof item.kwDifficulty === 'string' ? item.kwDifficulty : 
                        item.kwDifficulty <= 1.5 ? 'LOW' : 
                        item.kwDifficulty <= 2.5 ? 'MEDIUM' : 'HIGH';
      return difficulty === 'LOW' && item.msv > 20;
    }
  ).length;

  return (
    <div className="bg-white shadow-md rounded-lg p-6 mb-8">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Content Matrix Results</h2>
        <div className="text-sm text-gray-600">
          <span className="font-medium">Keyword:</span> {mainKeyword} | 
          <span className="font-medium ml-2">Location:</span> {location} | 
          <span className="font-medium ml-2">Language:</span> {language}
        </div>
      </div>

      {/* Key Metrics Grid - Only 4 KPIs */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
          <div className="text-2xl font-bold text-blue-600">{totalKeywords}</div>
          <div className="text-sm text-blue-800">Total Keywords</div>
        </div>
        <div className="bg-green-50 p-4 rounded-lg border border-green-200">
          <div className="text-2xl font-bold text-green-600">{totalContentPieces}</div>
          <div className="text-sm text-green-800">Content Pieces</div>
        </div>
        <div className="bg-purple-50 p-4 rounded-lg border border-purple-200">
          <div className={`text-2xl font-bold ${
            avgDifficultyLabel === 'LOW' ? 'text-green-600' :
            avgDifficultyLabel === 'MEDIUM' ? 'text-yellow-600' : 'text-red-600'
          }`}>
            {avgDifficultyLabel}
          </div>
          <div className="text-sm text-purple-800">Keyword Difficulty</div>
        </div>
        <div className="bg-orange-50 p-4 rounded-lg border border-orange-200">
          <div className="text-2xl font-bold text-orange-600">{highOpportunityKeywords}</div>
          <div className="text-sm text-orange-800">High Opportunity</div>
        </div>
      </div>
    </div>
  );
};

export default MatrixKPIs;
