import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// Define public routes that don't require authentication
const publicRoutes = [
  '/',
  '/login-with-otp',
  '/signup',
  '/about',
  '/privacy',
  '/terms',
  '/contact',
  '/faq',
  '/pricing',
  '/payment/success',
  '/payment/failure',
];

// Define API routes that should be accessible without authentication
const publicApiRoutes = [
  '/api/auth/signin',
  '/api/auth/signup',
  '/api/auth/send-otp',
  '/api/auth/verify-otp',
  '/api/webhook-proxy',
  '/api/matrix-webhook-proxy',
  '/api/payment/callback',
  '/api/payment/webhook',
];

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Skip middleware for API routes and public routes
  if (
    pathname.startsWith('/api/') ||
    pathname.startsWith('/_next/') ||
    pathname.startsWith('/favicon') ||
    pathname.includes('.') ||
    publicRoutes.some(route => pathname === route) ||
    publicApiRoutes.some(route => pathname.startsWith(route))
  ) {
    return NextResponse.next();
  }

  // Check for the auth token in the cookies
  const authToken = request.cookies.get('auth_token')?.value;

  // If no token and trying to access protected route, redirect to login
  if (!authToken && !pathname.startsWith('/login-with-otp') && !pathname.startsWith('/signup')) {
    const url = new URL('/login-with-otp', request.url);
    return NextResponse.redirect(url);
  }

  // If token exists and user is on login or signup page, redirect to dashboard
  if (authToken && (pathname.startsWith('/login-with-otp') || pathname.startsWith('/signup'))) {
    const url = new URL('/dashboard', request.url);
    return NextResponse.redirect(url);
  }

  return NextResponse.next();
}

// Configure the middleware to run on specific paths
export const config = {
  matcher: [
    /*
     * Match all request paths except:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|public).*)',
  ],
};
