/**
 * Utility functions for interacting with webhooks
 */

// The direct webhook URL from environment
const DIRECT_WEBHOOK_URL = process.env.NEXT_PUBLIC_OTP_WEBHOOK_URL;

// Proxy URL for client-side requests
const WEBHOOK_PROXY_URL = '/api/webhook-proxy';

/**
 * Interface for OTP data to be sent to webhook
 */
interface OTPWebhookData {
  name: string;
  email: string;
  otp: string;
  route: string; // 'signin', 'signup', 'send-otp', or 'verify-otp'
  plan?: string; // 'free', 'standard', or 'pro'
  timestamp?: number;
}

/**
 * Send OTP data to the webhook
 * @param data The OTP data to send
 * @returns Promise resolving to the response from the webhook
 */
/**
 * Interface for OTP verification data to be sent to webhook
 */
interface OTPVerificationData {
  name: string;
  email: string;
  status: 'verified';
  route: 'verify-otp';
  plan?: string; // 'free', 'standard', or 'pro'
  timestamp?: number;
}

export const sendOTPToWebhook = async (data: OTPWebhookData): Promise<any> => {
  // Determine if we're running on the server or client
  const isServer = typeof window === 'undefined';

  // Log environment for debugging
  console.log('Running on:', isServer ? 'server-side' : 'client-side');
  console.log('Environment variable for webhook URL:', process.env.NEXT_PUBLIC_OTP_WEBHOOK_URL);

  try {
    // Add timestamp if not provided and include debug info
    const webhookData = {
      ...data,
      timestamp: data.timestamp || Date.now(),
      debug: {
        source: 'matrix-app',
        clientTime: new Date().toISOString(),
        environment: process.env.NODE_ENV || 'unknown',
        runtime: isServer ? 'server' : 'browser',
      }
    };

    // Debug log the data being sent
    console.log('Sending data to webhook:', JSON.stringify(webhookData, null, 2));

    // Determine which URL to use
    // If on server, use the direct webhook URL
    // If on client, use the proxy URL
    let targetUrl: string;

    if (isServer) {
      // On server, use direct webhook URL
      if (!DIRECT_WEBHOOK_URL) {
        console.error('Webhook URL is not configured. Using fallback mechanism.');
        // Instead of throwing an error, return a mock success response
        return {
          success: true,
          isSent: true,
          message: "OTP would be sent in production environment"
        };
      }
      targetUrl = DIRECT_WEBHOOK_URL;
      console.log('Using direct webhook URL (server-side):', targetUrl);
    } else {
      // On client, use the proxy URL via the current origin
      targetUrl = new URL(WEBHOOK_PROXY_URL, window.location.origin).toString();
      console.log('Using proxy webhook URL (client-side):', targetUrl);
    }

    // Add detailed logging for the fetch call
    console.log('Starting fetch call to webhook...');

    // Send data with timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => {
      console.log('Webhook request timed out after 10 seconds');
      controller.abort();
    }, 10000);

    try {
      const response = await fetch(targetUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Debug-Info': `OTP webhook call from Matrix app (${isServer ? 'server' : 'client'})`,
        },
        body: JSON.stringify(webhookData),
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      // Debug log the response status
      console.log('Webhook proxy response status:', response.status);
      console.log('Webhook proxy response headers:', Object.fromEntries([...response.headers.entries()]));

      // Parse response
      const responseText = await response.text();
      console.log('Webhook proxy raw response:', responseText);

      let responseData;
      try {
        responseData = JSON.parse(responseText);
      } catch (e) {
        console.error('Error parsing JSON response:', e);
        responseData = { success: false, error: 'Invalid JSON response', rawResponse: responseText };
      }

      // Log success with response data
      console.log(`OTP data sent to webhook for ${data.email}. Response:`, JSON.stringify(responseData, null, 2));

      return responseData;
    } catch (fetchError) {
      clearTimeout(timeoutId);
      console.error('Fetch error during webhook proxy call:', fetchError);
      throw fetchError;
    }
  } catch (error) {
    // Log detailed error
    console.error('Error sending OTP data to webhook:', error);
    console.error('Error details:', error instanceof Error ? error.message : String(error));
    console.error('Webhook proxy URL used:', WEBHOOK_PROXY_URL);
    throw error;
  }
};

/**
 * Send OTP verification signal to the webhook
 * @param name User's name
 * @param email User's email
 * @returns Promise resolving to the response from the webhook
 */
export const sendOTPVerificationToWebhook = async (name: string, email: string, plan?: string): Promise<any> => {
  // Create verification data
  const verificationData: OTPVerificationData = {
    name,
    email,
    status: 'verified',
    route: 'verify-otp',
    plan, // Include the plan if provided
    timestamp: Date.now(),
  };

  // Use the existing webhook function to send the data
  return sendOTPToWebhook(verificationData as unknown as OTPWebhookData);
};

/**
 * Interface for matrix generation data to be sent to webhook
 */
interface MatrixGenerationData {
  userId: string;
  plan: string;
  mainKeyword: string;
  location: string;
  language: string;
  timestamp?: number;
}

/**
 * Send matrix generation request to the webhook
 * @param data The matrix generation data to send
 * @returns Promise resolving to the response from the webhook
 */
export const sendMatrixGenerationToWebhook = async (data: MatrixGenerationData): Promise<any> => {
  const MATRIX_WEBHOOK_URL = 'https://n8n.taqnik.in/webhook/b42ac69e-6290-4202-9f5f-930c2685a7b2';

  try {
    // Add timestamp if not provided and include debug info
    const webhookData = {
      ...data,
      timestamp: data.timestamp || Date.now(),
      debug: {
        source: 'matrix-app',
        clientTime: new Date().toISOString(),
        environment: process.env.NODE_ENV || 'unknown',
        action: 'matrix-generation'
      }
    };

    // Debug log the data being sent
    console.log('Sending matrix generation data to webhook:', JSON.stringify(webhookData, null, 2));

    // Send data with timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => {
      console.log('Matrix webhook request timed out after 30 seconds');
      controller.abort();
    }, 30000); // 30 seconds timeout for matrix generation

    try {
      const response = await fetch(MATRIX_WEBHOOK_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Debug-Info': 'Matrix generation webhook call from Matrix app',
        },
        body: JSON.stringify(webhookData),
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      // Debug log the response status
      console.log('Matrix webhook response status:', response.status);
      console.log('Matrix webhook response headers:', Object.fromEntries([...response.headers.entries()]));

      // Parse response
      const responseText = await response.text();
      console.log('Matrix webhook raw response:', responseText);

      let responseData;
      try {
        responseData = JSON.parse(responseText);
      } catch (e) {
        console.error('Error parsing JSON response:', e);
        responseData = { success: false, error: 'Invalid JSON response', rawResponse: responseText };
      }

      // Log success with response data
      console.log(`Matrix generation data sent to webhook for user ${data.userId}. Response:`, JSON.stringify(responseData, null, 2));

      return responseData;
    } catch (fetchError) {
      clearTimeout(timeoutId);
      console.error('Fetch error during matrix webhook call:', fetchError);
      throw fetchError;
    }
  } catch (error) {
    // Log detailed error
    console.error('Error sending matrix generation data to webhook:', error);
    console.error('Error details:', error instanceof Error ? error.message : String(error));
    throw error;
  }
};
