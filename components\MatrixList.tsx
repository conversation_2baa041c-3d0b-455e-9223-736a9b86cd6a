'use client';

import React from 'react';
import Link from 'next/link';
import MatrixCard from './MatrixCard';
import { Matrix } from '@/types';

interface MatrixListProps {
  matrices: Matrix[];
  projectId: string;
  projectName?: string;
}

const MatrixList: React.FC<MatrixListProps> = ({ matrices, projectId, projectName }) => {
  // Create a slug from the project name if provided, otherwise use the projectId
  const projectSlug = projectName
    ? projectName.toLowerCase().replace(/\s+/g, '-')
    : projectId;
  if (!matrices || matrices.length === 0) {
    return (
      <div className="text-center py-12">
        <Link
          href={`/project/${projectSlug}/new-matrix`}
          className="inline-block"
        >
          <div className="mx-auto w-40 h-40 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center mb-4 hover:border-indigo-300 transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-gray-400 hover:text-indigo-500 transition-colors" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
            </svg>
          </div>
        </Link>
        <h2 className="text-xl font-semibold text-gray-800 mb-2">Create Matrix</h2>
        <p className="text-gray-600">Generate your first SEO Content Matrix for this project.</p>
      </div>
    );
  }
  console.log('Rendering MatrixList with matrices:', matrices);

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900">Content Matrices</h2>
        <Link
          href={`/project/${projectSlug}/new-matrix`}
          className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
          </svg>
          New Matrix
        </Link>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {matrices.map((matrix) => (
          <MatrixCard key={matrix._id} matrix={matrix} projectId={projectId} projectName={projectName} />
        ))}
      </div>
    </div>
  );
};

export default MatrixList;
