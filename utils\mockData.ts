import { KeywordResearchItem, ContentMatrixItem, WebhookData } from '@/types';

// Enhanced function to generate realistic keyword research data
export const generateMockKeywordResearch = (
  mainKeyword: string,
  count: number = 25
): KeywordResearchItem[] => {
  // Create realistic keyword variations based on the main keyword
  const getKeywordVariations = (keyword: string) => {
    const variations = [
      `free ai tools for ${keyword}`,
      `${keyword} for office work`,
      `${keyword} for students`,
      `best ai tools for students reddit`,
      `ai tools for productivity`,
      `best free ai tools for office work`,
      `best free ai tools list`,
      `best ai tools for business reddit`,
      `best ai tools for business productivity`,
      `free ai tools for productivity`,
      `ai tools for business productivity`,
      `ai tools for developer productivity`,
      `ai tools for business`,
      `best free ai tools for productivity`,
      `best ai tools for workplace productivity`,
      `ai productivity tools for writing at work`,
      `ai tools for office productivity`,
      `ai productivity tools for spreadsheets`,
      `ai tools for personal productivity`,
      `productivity tools for work`,
      `top 10 ai tools for productivity`,
      `generative ai tools for better productivity`,
      `ai productivity tools for writing`,
      `ai tools for productivity reddit`,
      `ai tools for productivity`
    ];
    return variations;
  };

  const variations = getKeywordVariations(mainKeyword);
  const searchIntents = ['transactional', 'commercial', 'informational'];
  const difficulties = ['LOW', 'MEDIUM', 'HIGH'];
  const competitionValues = [0.1, 0.3, 0.5, 0.7, 0.9]; // Numeric competition values

  // Realistic MSV values
  const msvValues = [40, 30, 30, 20, 10, 10, 10, 10, 19, 357, 29, 36, 56, 29, 100, 100, 0, 0, 0, 0, 0, 0, 0, 0, 0];

  // Realistic CPC values
  const cpcValues = [4.38, 7.03, 2.77, 3.06, 2.32, 30.44, 51.03, 11.07, 13.22, 7.2, 8.73, 16.5, 3.06, 13.39, 2.05];

  return variations.slice(0, count).map((keyword, i) => ({
    mainKeyword,
    type: 'Related',
    keyword: keyword,
    msv: msvValues[i] || Math.floor(Math.random() * 100),
    searchIntent: searchIntents[i % searchIntents.length],
    kwDifficulty: difficulties[Math.floor(Math.random() * difficulties.length)],
    competition: competitionValues[Math.floor(Math.random() * competitionValues.length)],
    cpc: cpcValues[i] || parseFloat((Math.random() * 20).toFixed(2)),
    answer: i % 4 === 0 ? 'Featured snippet available' : '',
    timestamp: new Date().toISOString(),
  }));
};

// Enhanced function to generate realistic content matrix data
export const generateMockContentMatrix = (
  mainKeyword: string,
  count: number = 15
): ContentMatrixItem[] => {
  // Realistic content clusters and topics
  const contentData = [
    {
      cluster: 'AI Tools Overview',
      contentType: 'Pillar Page',
      focus: 'Complete Guide to AI Tools for Productivity',
      category: 'Educational',
      url: '/ai-tools-productivity-complete-guide',
      searchVol: 1200,
      keywords: 'ai tools, productivity tools, artificial intelligence, automation',
      status: 'Published'
    },
    {
      cluster: 'Free AI Tools',
      contentType: 'List Article',
      focus: 'Top 15 Free AI Tools for Office Work',
      category: 'Resource',
      url: '/free-ai-tools-office-work',
      searchVol: 890,
      keywords: 'free ai tools, office productivity, workplace automation',
      status: 'Published'
    },
    {
      cluster: 'Student Tools',
      contentType: 'Guide',
      focus: 'Best AI Tools for Students in 2024',
      category: 'Educational',
      url: '/ai-tools-students-2024',
      searchVol: 650,
      keywords: 'ai tools students, study productivity, academic tools',
      status: 'Draft'
    },
    {
      cluster: 'Business Productivity',
      contentType: 'Comparison',
      focus: 'AI Tools vs Traditional Productivity Software',
      category: 'Analysis',
      url: '/ai-tools-vs-traditional-software',
      searchVol: 420,
      keywords: 'ai productivity comparison, business tools, efficiency',
      status: 'In Progress'
    },
    {
      cluster: 'Writing Tools',
      contentType: 'Review',
      focus: 'AI Writing Tools for Content Creation',
      category: 'Tool Review',
      url: '/ai-writing-tools-review',
      searchVol: 780,
      keywords: 'ai writing, content creation, copywriting tools',
      status: 'Published'
    },
    {
      cluster: 'Developer Tools',
      contentType: 'Tutorial',
      focus: 'How to Integrate AI Tools in Development Workflow',
      category: 'Technical',
      url: '/ai-tools-development-workflow',
      searchVol: 340,
      keywords: 'ai development tools, coding productivity, developer workflow',
      status: 'Planned'
    },
    {
      cluster: 'Spreadsheet Automation',
      contentType: 'How-to Guide',
      focus: 'Automate Spreadsheets with AI Tools',
      category: 'Tutorial',
      url: '/automate-spreadsheets-ai',
      searchVol: 290,
      keywords: 'spreadsheet automation, excel ai, data processing',
      status: 'Draft'
    },
    {
      cluster: 'Personal Productivity',
      contentType: 'List Article',
      focus: '10 AI Tools to Boost Personal Productivity',
      category: 'Lifestyle',
      url: '/personal-productivity-ai-tools',
      searchVol: 560,
      keywords: 'personal productivity, life optimization, ai assistants',
      status: 'Published'
    },
    {
      cluster: 'Workplace Integration',
      contentType: 'Case Study',
      focus: 'How Companies Use AI for Workplace Productivity',
      category: 'Business',
      url: '/companies-ai-workplace-productivity',
      searchVol: 380,
      keywords: 'workplace ai, corporate productivity, business automation',
      status: 'In Progress'
    },
    {
      cluster: 'Tool Comparisons',
      contentType: 'Comparison',
      focus: 'ChatGPT vs Other AI Productivity Tools',
      category: 'Comparison',
      url: '/chatgpt-vs-ai-productivity-tools',
      searchVol: 920,
      keywords: 'chatgpt comparison, ai tool comparison, productivity software',
      status: 'Published'
    },
    {
      cluster: 'Getting Started',
      contentType: 'Beginner Guide',
      focus: 'Getting Started with AI Tools for Beginners',
      category: 'Educational',
      url: '/ai-tools-beginners-guide',
      searchVol: 710,
      keywords: 'ai tools beginners, getting started ai, productivity basics',
      status: 'Published'
    },
    {
      cluster: 'Advanced Features',
      contentType: 'Advanced Guide',
      focus: 'Advanced AI Tool Features for Power Users',
      category: 'Advanced',
      url: '/advanced-ai-tool-features',
      searchVol: 180,
      keywords: 'advanced ai features, power user tools, ai customization',
      status: 'Planned'
    },
    {
      cluster: 'Industry Specific',
      contentType: 'Industry Guide',
      focus: 'AI Tools for Marketing Professionals',
      category: 'Industry',
      url: '/ai-tools-marketing-professionals',
      searchVol: 450,
      keywords: 'marketing ai tools, digital marketing automation, martech',
      status: 'Draft'
    },
    {
      cluster: 'Cost Analysis',
      contentType: 'Analysis',
      focus: 'Free vs Paid AI Productivity Tools: Worth the Investment?',
      category: 'Financial',
      url: '/free-vs-paid-ai-tools-analysis',
      searchVol: 320,
      keywords: 'ai tools cost, productivity roi, tool investment',
      status: 'In Progress'
    },
    {
      cluster: 'Future Trends',
      contentType: 'Trend Analysis',
      focus: 'Future of AI in Workplace Productivity',
      category: 'Trends',
      url: '/future-ai-workplace-productivity',
      searchVol: 240,
      keywords: 'ai future trends, workplace evolution, productivity innovation',
      status: 'Planned'
    }
  ];

  return contentData.slice(0, count).map((item, i) => ({
    mainKeyword,
    cluster: item.cluster,
    contentType: item.contentType,
    focus: item.focus,
    category: item.category,
    url: item.url,
    searchVol: item.searchVol,
    keywords: item.keywords,
    status: item.status,
  }));
};

// Function to generate complete mock webhook data
export const generateMockWebhookData = (
  mainKeyword: string,
  location: string = 'United States',
  language: string = 'English',
  limit: number = 25
): WebhookData => {
  return {
    mainKeyword,
    location,
    language,
    limit,
    date: new Date().toISOString().split('T')[0],
    timestamp: new Date().toISOString(),
    keywordResearch: generateMockKeywordResearch(mainKeyword, 25),
    contentMatrix: generateMockContentMatrix(mainKeyword, 15),
  };
};
