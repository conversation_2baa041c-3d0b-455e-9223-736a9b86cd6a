// Test script to verify webhook implementation
// Run this with: node test-webhook.js

const testWebhookCall = async () => {
  const webhookData = {
    userId: 'test-user-123',
    plan: 'standard',
    mainKeyword: 'ai tools for productivity',
    location: 'United States',
    language: 'English',
    timestamp: Date.now(),
    debug: {
      source: 'matrix-app',
      clientTime: new Date().toISOString(),
      environment: 'test',
      action: 'matrix-generation'
    }
  };

  const MATRIX_WEBHOOK_URL = 'https://n8n.taqnik.in/webhook/b42ac69e-6290-4202-9f5f-930c2685a7b2';

  try {
    console.log('🚀 Testing webhook call...');
    console.log('📤 Sending data:', JSON.stringify(webhookData, null, 2));

    const response = await fetch(MATRIX_WEBHOOK_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Debug-Info': 'Test webhook call from Matrix app',
      },
      body: JSON.stringify(webhookData),
    });

    console.log('📥 Response status:', response.status);
    console.log('📥 Response headers:', Object.fromEntries([...response.headers.entries()]));

    const responseText = await response.text();
    console.log('📥 Raw response:', responseText);

    let responseData;
    try {
      responseData = JSON.parse(responseText);
      console.log('✅ Parsed response:', JSON.stringify(responseData, null, 2));
    } catch (e) {
      console.log('⚠️ Response is not JSON:', responseText);
    }

    if (response.ok) {
      console.log('✅ Webhook test successful!');
    } else {
      console.log('❌ Webhook test failed with status:', response.status);
    }

  } catch (error) {
    console.error('❌ Webhook test error:', error);
  }
};

// Run the test
testWebhookCall();
