/**
 * Generate a consistent matrix ID based on the main keyword
 * This ensures the same keyword always generates the same matrix ID
 */
export const generateMatrixId = (mainKeyword: string): string => {
  // Convert keyword to a clean slug format
  const keywordSlug = mainKeyword
    .toLowerCase()
    .trim()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters except spaces and hyphens
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
    .replace(/^-|-$/g, ''); // Remove leading/trailing hyphens
  
  return keywordSlug;
};

/**
 * Extract keyword from matrix ID
 * Converts matrix ID back to readable keyword format
 */
export const extractKeywordFromMatrixId = (matrixId: string): string => {
  if (!matrixId || matrixId === 'undefined') {
    return 'ai tools for productivity'; // Default fallback
  }
  
  // Convert slug back to readable format
  return matrixId
    .replace(/-/g, ' ')
    .toLowerCase()
    .replace(/\b\w/g, char => char.toUpperCase()); // Capitalize first letter of each word
};

/**
 * Validate if a matrix ID is valid
 */
export const isValidMatrixId = (matrixId: string): boolean => {
  return matrixId && matrixId !== 'undefined' && matrixId.trim().length > 0;
};
